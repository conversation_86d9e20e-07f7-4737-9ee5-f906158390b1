import inventory from "../assets/inventory.png";
import Data from "../ProductData/Data";
import editIcon from "../assets/edit.png";
import deleteIcon from "../assets/delete.png";
import "./Product.css";
import { useState, useEffect } from "react";

export default function Product() {
  const [filteredUsers, setFilteredUsers] = useState(Data);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    // If nothing is entered → show all
    if (searchTerm.trim() === "") {
      setFilteredUsers(Data);
    } else {
      // Otherwise → filter
      setFilteredUsers(
        Data.filter((user) =>
          user.name.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }
  }, [searchTerm]);

  const searchChangeHandler = (event) => {
    setSearchTerm(event.target.value);
  };

  return (
    <div className="container mt-4">
      {/* Header Section */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1 className="d-flex align-items-center">
          <img
            src={inventory}
            className="me-2"
            alt="logo"
            width={40}
            height={40}
          />
          Inventory List
        </h1>
        <input
          type="text"
          className="form-control w-25"
          placeholder="Search..."
          value={searchTerm}
          onChange={searchChangeHandler}
        />
      </div>

      {/* Table Section */}
      <div className="table-responsive shadow-sm rounded">
        <table className="table table-striped table-bordered table-hover align-middle">
          <thead className="table-dark">
            <tr>
              <th>Name</th>
              <th>Category</th>
              <th>Size</th>
              <th>Current Quantity</th>
              <th>Price</th>
              <th>Minimum Stock</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredUsers.map((value, index) => (
              <tr key={index}>
                <td>{value.name}</td>
                <td>{value.category}</td>
                <td>{value.size}</td>
                <td className={value.currentQuantity}>
                  {value.currentQuantity}
                </td>
                <td>₹{value.price}</td>
                <td>{value.minimumStock}</td>
                <td>
                  <button className="btn btn-edit me-2">
                    <img src={editIcon} alt="Edit" width={18} height={18} />
                  </button>
                  <button className="btn btn-delete">
                    <img src={deleteIcon} alt="Delete" width={18} height={18} />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
