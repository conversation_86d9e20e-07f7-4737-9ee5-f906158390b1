import express from "express";
import mysql from "mysql2";
import cors from "cors";
import dotenv from "dotenv";

dotenv.config();
const app = express();
app.use(cors());
app.use(express.json());

// MySQL connection
const db = mysql.createConnection({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASS,
  database: process.env.DB_NAME,
});

db.connect((err) => {
  if (err) {
    console.error("❌ MySQL connection error:", err);
  } else {
    console.log("✅ MySQL Connected!");
  }
});

// Validation middleware
const validateProduct = (req, res, next) => {
  const { name, price, stock } = req.body;

  if (!name || typeof name !== "string" || name.trim().length === 0) {
    return res.status(400).json({
      error: "Name is required and must be a non-empty string",
    });
  }

  if (price !== undefined && (typeof price !== "number" || price < 0)) {
    return res.status(400).json({
      error: "Price must be a non-negative number",
    });
  }

  if (stock !== undefined && (typeof stock !== "number" || stock < 0)) {
    return res.status(400).json({
      error: "Stock must be a non-negative number",
    });
  }

  next();
};

// Routes
// Health check
app.get("/api/health", (req, res) => {
  res.json({
    status: "OK",
    message: "Inventory API is running",
    timestamp: new Date().toISOString(),
  });
});

// Get all products with search and pagination
app.get("/api/products", (req, res) => {
  const {
    search,
    page = 1,
    limit = 10,
    sortBy = "id",
    sortOrder = "ASC",
  } = req.query;

  let query = "SELECT * FROM products";
  let countQuery = "SELECT COUNT(*) as total FROM products";
  let queryParams = [];

  if (search) {
    const searchCondition = " WHERE name LIKE ? OR description LIKE ?";
    query += searchCondition;
    countQuery += searchCondition;
    queryParams = [`%${search}%`, `%${search}%`];
  }

  // Add sorting
  const validSortColumns = ["id", "name", "price", "stock", "created_at"];
  const validSortOrders = ["ASC", "DESC"];

  if (
    validSortColumns.includes(sortBy) &&
    validSortOrders.includes(sortOrder.toUpperCase())
  ) {
    query += ` ORDER BY ${sortBy} ${sortOrder.toUpperCase()}`;
  }

  // Add pagination
  const offset = (parseInt(page) - 1) * parseInt(limit);
  query += ` LIMIT ${parseInt(limit)} OFFSET ${offset}`;

  // Get total count first
  db.query(countQuery, queryParams, (err, countResult) => {
    if (err) {
      console.error("Database error:", err);
      return res.status(500).json({ error: "Failed to fetch products count" });
    }

    const total = countResult[0].total;

    // Get products
    db.query(query, queryParams, (err, results) => {
      if (err) {
        console.error("Database error:", err);
        return res.status(500).json({ error: "Failed to fetch products" });
      }

      res.json({
        products: results,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: total,
          totalPages: Math.ceil(total / parseInt(limit)),
        },
      });
    });
  });
});

// Get single product by ID
app.get("/api/products/:id", (req, res) => {
  const { id } = req.params;

  if (!id || isNaN(id)) {
    return res.status(400).json({ error: "Invalid product ID" });
  }

  db.query("SELECT * FROM products WHERE id = ?", [id], (err, results) => {
    if (err) {
      console.error("Database error:", err);
      return res.status(500).json({ error: "Failed to fetch product" });
    }

    if (results.length === 0) {
      return res.status(404).json({ error: "Product not found" });
    }

    res.json(results[0]);
  });
});

// Add product
app.post("/api/products", validateProduct, (req, res) => {
  const {
    name,
    price = 0,
    stock = 0,
    description = "",
    category = "",
    sku = "",
  } = req.body;

  const query = `
    INSERT INTO products (name, price, stock, description, category, sku, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
  `;

  db.query(
    query,
    [name, price, stock, description, category, sku],
    (err, result) => {
      if (err) {
        console.error("Database error:", err);
        if (err.code === "ER_DUP_ENTRY") {
          return res
            .status(409)
            .json({ error: "Product with this SKU already exists" });
        }
        return res.status(500).json({ error: "Failed to create product" });
      }

      // Return the created product
      db.query(
        "SELECT * FROM products WHERE id = ?",
        [result.insertId],
        (err, results) => {
          if (err) {
            console.error("Database error:", err);
            return res
              .status(500)
              .json({ error: "Product created but failed to fetch details" });
          }

          res.status(201).json(results[0]);
        }
      );
    }
  );
});

// Update product
app.put("/api/products/:id", validateProduct, (req, res) => {
  const { id } = req.params;
  const { name, price, stock, description, category, sku } = req.body;

  if (!id || isNaN(id)) {
    return res.status(400).json({ error: "Invalid product ID" });
  }

  // Check if product exists
  db.query("SELECT * FROM products WHERE id = ?", [id], (err, results) => {
    if (err) {
      console.error("Database error:", err);
      return res
        .status(500)
        .json({ error: "Failed to check product existence" });
    }

    if (results.length === 0) {
      return res.status(404).json({ error: "Product not found" });
    }

    const updateFields = [];
    const updateValues = [];

    if (name !== undefined) {
      updateFields.push("name = ?");
      updateValues.push(name);
    }
    if (price !== undefined) {
      updateFields.push("price = ?");
      updateValues.push(price);
    }
    if (stock !== undefined) {
      updateFields.push("stock = ?");
      updateValues.push(stock);
    }
    if (description !== undefined) {
      updateFields.push("description = ?");
      updateValues.push(description);
    }
    if (category !== undefined) {
      updateFields.push("category = ?");
      updateValues.push(category);
    }
    if (sku !== undefined) {
      updateFields.push("sku = ?");
      updateValues.push(sku);
    }

    updateFields.push("updated_at = NOW()");
    updateValues.push(id);

    const query = `UPDATE products SET ${updateFields.join(", ")} WHERE id = ?`;

    db.query(query, updateValues, (err, result) => {
      if (err) {
        console.error("Database error:", err);
        if (err.code === "ER_DUP_ENTRY") {
          return res
            .status(409)
            .json({ error: "Product with this SKU already exists" });
        }
        return res.status(500).json({ error: "Failed to update product" });
      }

      // Return the updated product
      db.query("SELECT * FROM products WHERE id = ?", [id], (err, results) => {
        if (err) {
          console.error("Database error:", err);
          return res
            .status(500)
            .json({ error: "Product updated but failed to fetch details" });
        }

        res.json(results[0]);
      });
    });
  });
});

// Delete product
app.delete("/api/products/:id", (req, res) => {
  const { id } = req.params;

  if (!id || isNaN(id)) {
    return res.status(400).json({ error: "Invalid product ID" });
  }

  // Check if product exists
  db.query("SELECT * FROM products WHERE id = ?", [id], (err, results) => {
    if (err) {
      console.error("Database error:", err);
      return res
        .status(500)
        .json({ error: "Failed to check product existence" });
    }

    if (results.length === 0) {
      return res.status(404).json({ error: "Product not found" });
    }

    db.query("DELETE FROM products WHERE id = ?", [id], (err) => {
      if (err) {
        console.error("Database error:", err);
        return res.status(500).json({ error: "Failed to delete product" });
      }

      res.json({
        message: "Product deleted successfully",
        deletedProduct: results[0],
      });
    });
  });
});

// Stock Alert (stock < threshold)
app.get("/api/stock-alert", (req, res) => {
  const { threshold = 5 } = req.query;

  db.query(
    "SELECT * FROM products WHERE stock < ? ORDER BY stock ASC",
    [parseInt(threshold)],
    (err, results) => {
      if (err) {
        console.error("Database error:", err);
        return res
          .status(500)
          .json({ error: "Failed to fetch low stock products" });
      }

      res.json({
        threshold: parseInt(threshold),
        lowStockProducts: results,
        count: results.length,
      });
    }
  );
});

// Get products by category
app.get("/api/products/category/:category", (req, res) => {
  const { category } = req.params;
  const { page = 1, limit = 10 } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);

  // Get total count
  db.query(
    "SELECT COUNT(*) as total FROM products WHERE category = ?",
    [category],
    (err, countResult) => {
      if (err) {
        console.error("Database error:", err);
        return res
          .status(500)
          .json({ error: "Failed to fetch category products count" });
      }

      const total = countResult[0].total;

      // Get products
      db.query(
        "SELECT * FROM products WHERE category = ? ORDER BY name ASC LIMIT ? OFFSET ?",
        [category, parseInt(limit), offset],
        (err, results) => {
          if (err) {
            console.error("Database error:", err);
            return res
              .status(500)
              .json({ error: "Failed to fetch category products" });
          }

          res.json({
            category: category,
            products: results,
            pagination: {
              page: parseInt(page),
              limit: parseInt(limit),
              total: total,
              totalPages: Math.ceil(total / parseInt(limit)),
            },
          });
        }
      );
    }
  );
});

// Get all categories
app.get("/api/categories", (req, res) => {
  db.query(
    "SELECT category, COUNT(*) as productCount FROM products WHERE category != '' GROUP BY category ORDER BY category ASC",
    (err, results) => {
      if (err) {
        console.error("Database error:", err);
        return res.status(500).json({ error: "Failed to fetch categories" });
      }

      res.json({
        categories: results,
        totalCategories: results.length,
      });
    }
  );
});

// Bulk update stock
app.patch("/api/products/bulk-stock", (req, res) => {
  const { updates } = req.body; // Array of {id, stock}

  if (!Array.isArray(updates) || updates.length === 0) {
    return res
      .status(400)
      .json({ error: "Updates array is required and must not be empty" });
  }

  // Validate each update
  for (const update of updates) {
    if (
      !update.id ||
      isNaN(update.id) ||
      typeof update.stock !== "number" ||
      update.stock < 0
    ) {
      return res.status(400).json({
        error: "Each update must have valid id and non-negative stock number",
      });
    }
  }

  const promises = updates.map((update) => {
    return new Promise((resolve, reject) => {
      db.query(
        "UPDATE products SET stock = ?, updated_at = NOW() WHERE id = ?",
        [update.stock, update.id],
        (err, result) => {
          if (err) {
            reject(err);
          } else {
            resolve({
              id: update.id,
              stock: update.stock,
              affected: result.affectedRows,
            });
          }
        }
      );
    });
  });

  Promise.all(promises)
    .then((results) => {
      res.json({
        message: "Bulk stock update completed",
        updates: results,
        totalUpdated: results.filter((r) => r.affected > 0).length,
      });
    })
    .catch((err) => {
      console.error("Bulk update error:", err);
      res.status(500).json({ error: "Failed to complete bulk stock update" });
    });
});

// Get inventory statistics
app.get("/api/stats", (req, res) => {
  const queries = {
    totalProducts: "SELECT COUNT(*) as count FROM products",
    totalValue: "SELECT SUM(price * stock) as value FROM products",
    lowStock: "SELECT COUNT(*) as count FROM products WHERE stock < 5",
    categories:
      "SELECT COUNT(DISTINCT category) as count FROM products WHERE category != ''",
    avgPrice: "SELECT AVG(price) as avg FROM products",
    totalStock: "SELECT SUM(stock) as total FROM products",
  };

  const stats = {};
  const queryPromises = Object.entries(queries).map(([key, query]) => {
    return new Promise((resolve, reject) => {
      db.query(query, (err, results) => {
        if (err) {
          reject(err);
        } else {
          stats[key] = results[0][Object.keys(results[0])[0]];
          resolve();
        }
      });
    });
  });

  Promise.all(queryPromises)
    .then(() => {
      res.json({
        statistics: stats,
        generatedAt: new Date().toISOString(),
      });
    })
    .catch((err) => {
      console.error("Stats query error:", err);
      res.status(500).json({ error: "Failed to fetch statistics" });
    });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error("Unhandled error:", err);
  res.status(500).json({
    error: "Internal server error",
    message: err.message,
  });
});

// 404 handler
app.use("*", (req, res) => {
  res.status(404).json({
    error: "Endpoint not found",
    path: req.originalUrl,
    method: req.method,
  });
});

// Start server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 API Documentation: http://localhost:${PORT}/api/health`);
});
