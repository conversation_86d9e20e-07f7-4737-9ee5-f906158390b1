{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AA4LA,sBA8GC;AAKD,0BAgBC;AAgHD,sBA+BC;AAED,oCA2BC;AAmJD,8BAEC;AAhoBD,MAAM,iBAAiB,GAAG,GAAG,CAAC;AAC9B,MAAM,UAAU,GAAG,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,CAAC;AAC5C,MAAM,QAAQ,GAAG,qBAAqB,CAAC;AACvC,MAAM,WAAW,GAAG,mCAAmC,CAAC;AAkFxD,MAAM,aAAa,GAA8B;IAC/C,UAAU;IACV,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,YAAY;IACZ,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;CACT,CAAC;AAEF;;GAEG;AACH,SAAS,UAAU,CAAC,GAAW;IAC7B,OAAO,GAAG,CAAC,OAAO,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;AACnD,CAAC;AAED;;GAEG;AACH,SAAS,MAAM,CAAC,GAAW;IACzB,OAAO,GAAG,CAAC,OAAO,CAAC,sBAAsB,EAAE,MAAM,CAAC,CAAC;AACrD,CAAC;AAiDD;;GAEG;AACH,MAAa,SAAS;IACpB,YACkB,MAAe,EACf,YAAqB;QADrB,WAAM,GAAN,MAAM,CAAS;QACf,iBAAY,GAAZ,YAAY,CAAS;IACpC,CAAC;CACL;AALD,8BAKC;AAED;;GAEG;AACH,MAAa,SAAU,SAAQ,SAAS;IACtC,YACE,OAAe,EACC,YAAgC;QAEhD,IAAI,IAAI,GAAG,OAAO,CAAC;QACnB,IAAI,YAAY;YAAE,IAAI,IAAI,KAAK,YAAY,EAAE,CAAC;QAC9C,IAAI,IAAI,oDAAoD,CAAC;QAC7D,KAAK,CAAC,IAAI,CAAC,CAAC;QALI,iBAAY,GAAZ,YAAY,CAAoB;IAMlD,CAAC;CACF;AAVD,8BAUC;AAED;;GAEG;AACH,SAAgB,KAAK,CAAC,GAAW,EAAE,UAAwB,EAAE;IAC3D,MAAM,EAAE,UAAU,GAAG,UAAU,EAAE,GAAG,OAAO,CAAC;IAC5C,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;IACvB,MAAM,MAAM,GAAoB,EAAE,CAAC;IACnC,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,GAAG,GAAG,CAAC,CAAC;IAEZ,SAAS,IAAI;QACX,IAAI,KAAK,GAAG,EAAE,CAAC;QAEf,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAChC,GAAG,CAAC;gBACF,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;YAC1B,CAAC,QAAQ,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;QAC3C,CAAC;aAAM,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;YAChC,IAAI,UAAU,GAAG,KAAK,CAAC;YAEvB,OAAO,KAAK,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;gBAC9B,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;oBACzB,KAAK,EAAE,CAAC;oBACR,UAAU,GAAG,CAAC,CAAC;oBACf,MAAM;gBACR,CAAC;gBAED,oCAAoC;gBACpC,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI;oBAAE,KAAK,EAAE,CAAC;gBAEnC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;YACxB,CAAC;YAED,IAAI,UAAU,EAAE,CAAC;gBACf,MAAM,IAAI,SAAS,CAAC,+BAA+B,UAAU,EAAE,EAAE,GAAG,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,SAAS,CAAC,mCAAmC,KAAK,EAAE,EAAE,GAAG,CAAC,CAAC;QACvE,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;QAC5B,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAC3B,MAAM,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;QAElC,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QAC/C,CAAC;aAAM,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;QACzE,CAAC;aAAM,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QAChE,CAAC;aAAM,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QACnE,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;IAE/C,SAAS,YAAY,CAAC,OAAkB;QACtC,MAAM,MAAM,GAAY,EAAE,CAAC;QAE3B,OAAO,IAAI,EAAE,CAAC;YACZ,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;YAC5B,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO;gBAAE,MAAM;YAElC,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACrD,IAAI,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC;gBACvB,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEtB,OAAO,GAAG,CAAC,IAAI,KAAK,MAAM,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACpD,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC;oBAClB,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;gBACtB,CAAC;gBAED,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,MAAM;oBACZ,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC;iBACxB,CAAC,CAAC;gBACH,SAAS;YACX,CAAC;YAED,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBACxD,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,IAAI,EAAE,KAAK,CAAC,KAAK;iBAClB,CAAC,CAAC;gBACH,SAAS;YACX,CAAC;YAED,IAAI,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;gBACvB,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,MAAM,EAAE,YAAY,CAAC,GAAG,CAAC;iBAC1B,CAAC,CAAC;gBACH,SAAS;YACX,CAAC;YAED,MAAM,IAAI,SAAS,CACjB,cAAc,KAAK,CAAC,IAAI,aAAa,KAAK,CAAC,KAAK,cAAc,OAAO,EAAE,EACvE,GAAG,CACJ,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,OAAO,IAAI,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;AACjD,CAAC;AAED;;GAEG;AACH,SAAgB,OAAO,CACrB,IAAU,EACV,UAAyC,EAAE;IAE3C,MAAM,EAAE,MAAM,GAAG,kBAAkB,EAAE,SAAS,GAAG,iBAAiB,EAAE,GAClE,OAAO,CAAC;IACV,MAAM,IAAI,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACpE,MAAM,EAAE,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IAE5D,OAAO,SAAS,IAAI,CAAC,SAAY,EAAO;QACtC,MAAM,CAAC,IAAI,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,IAAI,SAAS,CAAC,uBAAuB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACnE,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;AACJ,CAAC;AAKD,SAAS,gBAAgB,CACvB,MAAe,EACf,SAAiB,EACjB,MAAsB;IAEtB,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACpC,eAAe,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAC1C,CAAC;IAEF,OAAO,CAAC,IAAe,EAAE,EAAE;QACzB,MAAM,MAAM,GAAa,CAAC,EAAE,CAAC,CAAC;QAE9B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,CAAC,KAAK,EAAE,GAAG,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;QACzB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CACtB,KAAY,EACZ,SAAiB,EACjB,MAAsB;IAEtB,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM;QAAE,OAAO,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAEtD,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAC3B,MAAM,EAAE,GAAG,gBAAgB,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QAE7D,OAAO,CAAC,IAAI,EAAE,EAAE;YACd,MAAM,CAAC,KAAK,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;YACrC,IAAI,CAAC,OAAO,CAAC,MAAM;gBAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YACpC,OAAO,CAAC,EAAE,CAAC,CAAC;QACd,CAAC,CAAC;IACJ,CAAC;IAED,MAAM,WAAW,GAAG,MAAM,IAAI,UAAU,CAAC;IAEzC,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;QAClD,OAAO,CAAC,IAAI,EAAE,EAAE;YACd,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC/B,IAAI,KAAK,IAAI,IAAI;gBAAE,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YAE3C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAChD,MAAM,IAAI,SAAS,CAAC,aAAa,KAAK,CAAC,IAAI,2BAA2B,CAAC,CAAC;YAC1E,CAAC;YAED,OAAO;gBACL,KAAK;qBACF,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;oBACpB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;wBAC9B,MAAM,IAAI,SAAS,CACjB,aAAa,KAAK,CAAC,IAAI,IAAI,KAAK,kBAAkB,CACnD,CAAC;oBACJ,CAAC;oBAED,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC;gBAC5B,CAAC,CAAC;qBACD,IAAI,CAAC,SAAS,CAAC;aACnB,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,OAAO,CAAC,IAAI,EAAE,EAAE;QACd,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,KAAK,IAAI,IAAI;YAAE,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,MAAM,IAAI,SAAS,CAAC,aAAa,KAAK,CAAC,IAAI,kBAAkB,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC;AACJ,CAAC;AAyBD;;GAEG;AACH,SAAgB,KAAK,CACnB,IAAmB,EACnB,UAAuC,EAAE;IAEzC,MAAM,EAAE,MAAM,GAAG,kBAAkB,EAAE,SAAS,GAAG,iBAAiB,EAAE,GAClE,OAAO,CAAC;IACV,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAErD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;QAChC,IAAI,MAAM,KAAK,KAAK;YAAE,OAAO,UAAU,CAAC;QACxC,IAAI,GAAG,CAAC,IAAI,KAAK,OAAO;YAAE,OAAO,MAAM,CAAC;QACxC,OAAO,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC/D,CAAC,CAAC,CAAC;IAEH,OAAO,SAAS,KAAK,CAAC,KAAa;QACjC,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,CAAC;YAAE,OAAO,KAAK,CAAC;QAErB,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS;gBAAE,SAAS;YAEjC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACxB,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC;QAED,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC1B,CAAC,CAAC;AACJ,CAAC;AAED,SAAgB,YAAY,CAC1B,IAAmB,EACnB,UAA8C,EAAE;IAEhD,MAAM,EACJ,SAAS,GAAG,iBAAiB,EAC7B,GAAG,GAAG,IAAI,EACV,SAAS,GAAG,KAAK,EACjB,QAAQ,GAAG,IAAI,GAChB,GAAG,OAAO,CAAC;IACZ,MAAM,IAAI,GAAS,EAAE,CAAC;IACtB,MAAM,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IACnC,MAAM,OAAO,GAAa,EAAE,CAAC;IAE7B,KAAK,MAAM,KAAK,IAAI,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC;QAC3C,MAAM,IAAI,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACvE,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;YACjD,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,IAAI,OAAO,GAAG,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IAC1C,IAAI,QAAQ;QAAE,OAAO,IAAI,MAAM,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;IACtD,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;IAEpD,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC1C,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AAC1B,CAAC;AAED;;GAEG;AACH,SAAS,YAAY,CAAC,KAAoB,EAAE,IAAY;IACtD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QACzB,KAAK,MAAM,CAAC,IAAI,KAAK;YAAE,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IAC/C,CAAC;SAAM,CAAC;QACN,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAOD;;GAEG;AACH,QAAQ,CAAC,CAAC,OAAO,CACf,MAAe,EACf,KAAa,EACb,IAAiB;IAEjB,IAAI,KAAK,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;QAC5B,OAAO,MAAM,IAAI,CAAC;IACpB,CAAC;IAED,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAE5B,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAC3B,KAAK,MAAM,GAAG,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC;YACzD,KAAK,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;SAAM,CAAC;QACN,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;AAC1C,CAAC;AAED;;GAEG;AACH,SAAS,cAAc,CACrB,MAAmB,EACnB,SAAiB,EACjB,IAAU,EACV,YAAgC;IAEhC,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,SAAS,GAAG,EAAE,CAAC;IACnB,IAAI,kBAAkB,GAAG,IAAI,CAAC;IAE9B,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;QAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAC1B,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9B,SAAS,IAAI,KAAK,CAAC,KAAK,CAAC;YACzB,kBAAkB,KAAlB,kBAAkB,GAAK,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAC;YACvD,SAAS;QACX,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YACxD,IAAI,CAAC,kBAAkB,IAAI,CAAC,SAAS,EAAE,CAAC;gBACtC,MAAM,IAAI,SAAS,CACjB,wBAAwB,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE,EACnD,YAAY,CACb,CAAC;YACJ,CAAC;YAED,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC3B,MAAM,IAAI,IAAI,MAAM,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC;YAC3E,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,aAAa,CAAC;YAC1B,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjB,SAAS,GAAG,EAAE,CAAC;YACf,kBAAkB,GAAG,KAAK,CAAC;YAC3B,SAAS;QACX,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAS,MAAM,CAAC,SAAiB,EAAE,SAAiB;IAClD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,KAAK,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC;QACvE,OAAO,SAAS,MAAM,CAAC,SAAS,CAAC,MAAM,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;IAC/D,CAAC;IACD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,OAAO,SAAS,MAAM,CAAC,SAAS,CAAC,MAAM,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;IAC/D,CAAC;IACD,OAAO,SAAS,MAAM,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC;AACrE,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,MAAe;IACtC,IAAI,KAAK,GAAG,EAAE,CAAC;IACf,IAAI,CAAC,GAAG,CAAC,CAAC;IAEV,SAAS,IAAI,CAAC,KAAa;QACzB,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;QACzB,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;QAE1B,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAC1B,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACjC,SAAS;QACX,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC3B,KAAK,IAAI,IAAI,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC;YAC9C,SAAS;QACX,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC3B,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,SAAS;QACX,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAC9B,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,SAAS;QACX,CAAC;QAED,MAAM,IAAI,SAAS,CAAC,uBAAwB,KAAa,CAAC,IAAI,EAAE,CAAC,CAAC;IACpE,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAgB,SAAS,CAAC,IAAe;IACvC,OAAO,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACtC,CAAC;AAED;;GAEG;AACH,SAAS,UAAU,CAAC,IAAY;IAC9B,MAAM,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;IAC9B,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9E,CAAC;AAED;;GAEG;AACH,SAAS,cAAc,CAAC,KAAwB;IAC9C,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM;QAAE,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7E,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["const DEFAULT_DELIMITER = \"/\";\nconst NOOP_VALUE = (value: string) => value;\nconst ID_START = /^[$_\\p{ID_Start}]$/u;\nconst ID_CONTINUE = /^[$\\u200c\\u200d\\p{ID_Continue}]$/u;\n\n/**\n * Encode a string into another string.\n */\nexport type Encode = (value: string) => string;\n\n/**\n * Decode a string into another string.\n */\nexport type Decode = (value: string) => string;\n\nexport interface ParseOptions {\n  /**\n   * A function for encoding input strings.\n   */\n  encodePath?: Encode;\n}\n\nexport interface PathToRegexpOptions {\n  /**\n   * Matches the path completely without trailing characters. (default: `true`)\n   */\n  end?: boolean;\n  /**\n   * Allows optional trailing delimiter to match. (default: `true`)\n   */\n  trailing?: boolean;\n  /**\n   * Match will be case sensitive. (default: `false`)\n   */\n  sensitive?: boolean;\n  /**\n   * The default delimiter for segments. (default: `'/'`)\n   */\n  delimiter?: string;\n}\n\nexport interface MatchOptions extends PathToRegexpOptions {\n  /**\n   * Function for decoding strings for params, or `false` to disable entirely. (default: `decodeURIComponent`)\n   */\n  decode?: Decode | false;\n}\n\nexport interface CompileOptions {\n  /**\n   * Function for encoding input strings for output into the path, or `false` to disable entirely. (default: `encodeURIComponent`)\n   */\n  encode?: Encode | false;\n  /**\n   * The default delimiter for segments. (default: `'/'`)\n   */\n  delimiter?: string;\n}\n\ntype TokenType =\n  | \"{\"\n  | \"}\"\n  | \"wildcard\"\n  | \"param\"\n  | \"char\"\n  | \"escape\"\n  | \"end\"\n  // Reserved for use or ambiguous due to past use.\n  | \"(\"\n  | \")\"\n  | \"[\"\n  | \"]\"\n  | \"+\"\n  | \"?\"\n  | \"!\";\n\n/**\n * Tokenizer results.\n */\ninterface LexToken {\n  type: TokenType;\n  index: number;\n  value: string;\n}\n\nconst SIMPLE_TOKENS: Record<string, TokenType> = {\n  // Groups.\n  \"{\": \"{\",\n  \"}\": \"}\",\n  // Reserved.\n  \"(\": \"(\",\n  \")\": \")\",\n  \"[\": \"[\",\n  \"]\": \"]\",\n  \"+\": \"+\",\n  \"?\": \"?\",\n  \"!\": \"!\",\n};\n\n/**\n * Escape text for stringify to path.\n */\nfunction escapeText(str: string) {\n  return str.replace(/[{}()\\[\\]+?!:*\\\\]/g, \"\\\\$&\");\n}\n\n/**\n * Escape a regular expression string.\n */\nfunction escape(str: string) {\n  return str.replace(/[.+*?^${}()[\\]|/\\\\]/g, \"\\\\$&\");\n}\n\n/**\n * Plain text.\n */\nexport interface Text {\n  type: \"text\";\n  value: string;\n}\n\n/**\n * A parameter designed to match arbitrary text within a segment.\n */\nexport interface Parameter {\n  type: \"param\";\n  name: string;\n}\n\n/**\n * A wildcard parameter designed to match multiple segments.\n */\nexport interface Wildcard {\n  type: \"wildcard\";\n  name: string;\n}\n\n/**\n * A set of possible tokens to expand when matching.\n */\nexport interface Group {\n  type: \"group\";\n  tokens: Token[];\n}\n\n/**\n * A token that corresponds with a regexp capture.\n */\nexport type Key = Parameter | Wildcard;\n\n/**\n * A sequence of `path-to-regexp` keys that match capturing groups.\n */\nexport type Keys = Array<Key>;\n\n/**\n * A sequence of path match characters.\n */\nexport type Token = Text | Parameter | Wildcard | Group;\n\n/**\n * Tokenized path instance.\n */\nexport class TokenData {\n  constructor(\n    public readonly tokens: Token[],\n    public readonly originalPath?: string,\n  ) {}\n}\n\n/**\n * ParseError is thrown when there is an error processing the path.\n */\nexport class PathError extends TypeError {\n  constructor(\n    message: string,\n    public readonly originalPath: string | undefined,\n  ) {\n    let text = message;\n    if (originalPath) text += `: ${originalPath}`;\n    text += `; visit https://git.new/pathToRegexpError for info`;\n    super(text);\n  }\n}\n\n/**\n * Parse a string for the raw tokens.\n */\nexport function parse(str: string, options: ParseOptions = {}): TokenData {\n  const { encodePath = NOOP_VALUE } = options;\n  const chars = [...str];\n  const tokens: Array<LexToken> = [];\n  let index = 0;\n  let pos = 0;\n\n  function name() {\n    let value = \"\";\n\n    if (ID_START.test(chars[index])) {\n      do {\n        value += chars[index++];\n      } while (ID_CONTINUE.test(chars[index]));\n    } else if (chars[index] === '\"') {\n      let quoteStart = index;\n\n      while (index++ < chars.length) {\n        if (chars[index] === '\"') {\n          index++;\n          quoteStart = 0;\n          break;\n        }\n\n        // Increment over escape characters.\n        if (chars[index] === \"\\\\\") index++;\n\n        value += chars[index];\n      }\n\n      if (quoteStart) {\n        throw new PathError(`Unterminated quote at index ${quoteStart}`, str);\n      }\n    }\n\n    if (!value) {\n      throw new PathError(`Missing parameter name at index ${index}`, str);\n    }\n\n    return value;\n  }\n\n  while (index < chars.length) {\n    const value = chars[index];\n    const type = SIMPLE_TOKENS[value];\n\n    if (type) {\n      tokens.push({ type, index: index++, value });\n    } else if (value === \"\\\\\") {\n      tokens.push({ type: \"escape\", index: index++, value: chars[index++] });\n    } else if (value === \":\") {\n      tokens.push({ type: \"param\", index: index++, value: name() });\n    } else if (value === \"*\") {\n      tokens.push({ type: \"wildcard\", index: index++, value: name() });\n    } else {\n      tokens.push({ type: \"char\", index: index++, value });\n    }\n  }\n\n  tokens.push({ type: \"end\", index, value: \"\" });\n\n  function consumeUntil(endType: TokenType): Token[] {\n    const output: Token[] = [];\n\n    while (true) {\n      const token = tokens[pos++];\n      if (token.type === endType) break;\n\n      if (token.type === \"char\" || token.type === \"escape\") {\n        let path = token.value;\n        let cur = tokens[pos];\n\n        while (cur.type === \"char\" || cur.type === \"escape\") {\n          path += cur.value;\n          cur = tokens[++pos];\n        }\n\n        output.push({\n          type: \"text\",\n          value: encodePath(path),\n        });\n        continue;\n      }\n\n      if (token.type === \"param\" || token.type === \"wildcard\") {\n        output.push({\n          type: token.type,\n          name: token.value,\n        });\n        continue;\n      }\n\n      if (token.type === \"{\") {\n        output.push({\n          type: \"group\",\n          tokens: consumeUntil(\"}\"),\n        });\n        continue;\n      }\n\n      throw new PathError(\n        `Unexpected ${token.type} at index ${token.index}, expected ${endType}`,\n        str,\n      );\n    }\n\n    return output;\n  }\n\n  return new TokenData(consumeUntil(\"end\"), str);\n}\n\n/**\n * Compile a string to a template function for the path.\n */\nexport function compile<P extends ParamData = ParamData>(\n  path: Path,\n  options: CompileOptions & ParseOptions = {},\n) {\n  const { encode = encodeURIComponent, delimiter = DEFAULT_DELIMITER } =\n    options;\n  const data = typeof path === \"object\" ? path : parse(path, options);\n  const fn = tokensToFunction(data.tokens, delimiter, encode);\n\n  return function path(params: P = {} as P) {\n    const [path, ...missing] = fn(params);\n    if (missing.length) {\n      throw new TypeError(`Missing parameters: ${missing.join(\", \")}`);\n    }\n    return path;\n  };\n}\n\nexport type ParamData = Partial<Record<string, string | string[]>>;\nexport type PathFunction<P extends ParamData> = (data?: P) => string;\n\nfunction tokensToFunction(\n  tokens: Token[],\n  delimiter: string,\n  encode: Encode | false,\n) {\n  const encoders = tokens.map((token) =>\n    tokenToFunction(token, delimiter, encode),\n  );\n\n  return (data: ParamData) => {\n    const result: string[] = [\"\"];\n\n    for (const encoder of encoders) {\n      const [value, ...extras] = encoder(data);\n      result[0] += value;\n      result.push(...extras);\n    }\n\n    return result;\n  };\n}\n\n/**\n * Convert a single token into a path building function.\n */\nfunction tokenToFunction(\n  token: Token,\n  delimiter: string,\n  encode: Encode | false,\n): (data: ParamData) => string[] {\n  if (token.type === \"text\") return () => [token.value];\n\n  if (token.type === \"group\") {\n    const fn = tokensToFunction(token.tokens, delimiter, encode);\n\n    return (data) => {\n      const [value, ...missing] = fn(data);\n      if (!missing.length) return [value];\n      return [\"\"];\n    };\n  }\n\n  const encodeValue = encode || NOOP_VALUE;\n\n  if (token.type === \"wildcard\" && encode !== false) {\n    return (data) => {\n      const value = data[token.name];\n      if (value == null) return [\"\", token.name];\n\n      if (!Array.isArray(value) || value.length === 0) {\n        throw new TypeError(`Expected \"${token.name}\" to be a non-empty array`);\n      }\n\n      return [\n        value\n          .map((value, index) => {\n            if (typeof value !== \"string\") {\n              throw new TypeError(\n                `Expected \"${token.name}/${index}\" to be a string`,\n              );\n            }\n\n            return encodeValue(value);\n          })\n          .join(delimiter),\n      ];\n    };\n  }\n\n  return (data) => {\n    const value = data[token.name];\n    if (value == null) return [\"\", token.name];\n\n    if (typeof value !== \"string\") {\n      throw new TypeError(`Expected \"${token.name}\" to be a string`);\n    }\n\n    return [encodeValue(value)];\n  };\n}\n\n/**\n * A match result contains data about the path match.\n */\nexport interface MatchResult<P extends ParamData> {\n  path: string;\n  params: P;\n}\n\n/**\n * A match is either `false` (no match) or a match result.\n */\nexport type Match<P extends ParamData> = false | MatchResult<P>;\n\n/**\n * The match function takes a string and returns whether it matched the path.\n */\nexport type MatchFunction<P extends ParamData> = (path: string) => Match<P>;\n\n/**\n * Supported path types.\n */\nexport type Path = string | TokenData;\n\n/**\n * Transform a path into a match function.\n */\nexport function match<P extends ParamData>(\n  path: Path | Path[],\n  options: MatchOptions & ParseOptions = {},\n): MatchFunction<P> {\n  const { decode = decodeURIComponent, delimiter = DEFAULT_DELIMITER } =\n    options;\n  const { regexp, keys } = pathToRegexp(path, options);\n\n  const decoders = keys.map((key) => {\n    if (decode === false) return NOOP_VALUE;\n    if (key.type === \"param\") return decode;\n    return (value: string) => value.split(delimiter).map(decode);\n  });\n\n  return function match(input: string) {\n    const m = regexp.exec(input);\n    if (!m) return false;\n\n    const path = m[0];\n    const params = Object.create(null);\n\n    for (let i = 1; i < m.length; i++) {\n      if (m[i] === undefined) continue;\n\n      const key = keys[i - 1];\n      const decoder = decoders[i - 1];\n      params[key.name] = decoder(m[i]);\n    }\n\n    return { path, params };\n  };\n}\n\nexport function pathToRegexp(\n  path: Path | Path[],\n  options: PathToRegexpOptions & ParseOptions = {},\n) {\n  const {\n    delimiter = DEFAULT_DELIMITER,\n    end = true,\n    sensitive = false,\n    trailing = true,\n  } = options;\n  const keys: Keys = [];\n  const flags = sensitive ? \"\" : \"i\";\n  const sources: string[] = [];\n\n  for (const input of pathsToArray(path, [])) {\n    const data = typeof input === \"object\" ? input : parse(input, options);\n    for (const tokens of flatten(data.tokens, 0, [])) {\n      sources.push(toRegExpSource(tokens, delimiter, keys, data.originalPath));\n    }\n  }\n\n  let pattern = `^(?:${sources.join(\"|\")})`;\n  if (trailing) pattern += `(?:${escape(delimiter)}$)?`;\n  pattern += end ? \"$\" : `(?=${escape(delimiter)}|$)`;\n\n  const regexp = new RegExp(pattern, flags);\n  return { regexp, keys };\n}\n\n/**\n * Convert a path or array of paths into a flat array.\n */\nfunction pathsToArray(paths: Path | Path[], init: Path[]): Path[] {\n  if (Array.isArray(paths)) {\n    for (const p of paths) pathsToArray(p, init);\n  } else {\n    init.push(paths);\n  }\n  return init;\n}\n\n/**\n * Flattened token set.\n */\ntype FlatToken = Text | Parameter | Wildcard;\n\n/**\n * Generate a flat list of sequence tokens from the given tokens.\n */\nfunction* flatten(\n  tokens: Token[],\n  index: number,\n  init: FlatToken[],\n): Generator<FlatToken[]> {\n  if (index === tokens.length) {\n    return yield init;\n  }\n\n  const token = tokens[index];\n\n  if (token.type === \"group\") {\n    for (const seq of flatten(token.tokens, 0, init.slice())) {\n      yield* flatten(tokens, index + 1, seq);\n    }\n  } else {\n    init.push(token);\n  }\n\n  yield* flatten(tokens, index + 1, init);\n}\n\n/**\n * Transform a flat sequence of tokens into a regular expression.\n */\nfunction toRegExpSource(\n  tokens: FlatToken[],\n  delimiter: string,\n  keys: Keys,\n  originalPath: string | undefined,\n): string {\n  let result = \"\";\n  let backtrack = \"\";\n  let isSafeSegmentParam = true;\n\n  for (const token of tokens) {\n    if (token.type === \"text\") {\n      result += escape(token.value);\n      backtrack += token.value;\n      isSafeSegmentParam ||= token.value.includes(delimiter);\n      continue;\n    }\n\n    if (token.type === \"param\" || token.type === \"wildcard\") {\n      if (!isSafeSegmentParam && !backtrack) {\n        throw new PathError(\n          `Missing text before \"${token.name}\" ${token.type}`,\n          originalPath,\n        );\n      }\n\n      if (token.type === \"param\") {\n        result += `(${negate(delimiter, isSafeSegmentParam ? \"\" : backtrack)}+)`;\n      } else {\n        result += `([\\\\s\\\\S]+)`;\n      }\n\n      keys.push(token);\n      backtrack = \"\";\n      isSafeSegmentParam = false;\n      continue;\n    }\n  }\n\n  return result;\n}\n\n/**\n * Block backtracking on previous text and ignore delimiter string.\n */\nfunction negate(delimiter: string, backtrack: string): string {\n  if (backtrack.length < 2) {\n    if (delimiter.length < 2) return `[^${escape(delimiter + backtrack)}]`;\n    return `(?:(?!${escape(delimiter)})[^${escape(backtrack)}])`;\n  }\n  if (delimiter.length < 2) {\n    return `(?:(?!${escape(backtrack)})[^${escape(delimiter)}])`;\n  }\n  return `(?:(?!${escape(backtrack)}|${escape(delimiter)})[\\\\s\\\\S])`;\n}\n\n/**\n * Stringify an array of tokens into a path string.\n */\nfunction stringifyTokens(tokens: Token[]): string {\n  let value = \"\";\n  let i = 0;\n\n  function name(value: string) {\n    const isSafe = isNameSafe(value) && isNextNameSafe(tokens[i]);\n    return isSafe ? value : JSON.stringify(value);\n  }\n\n  while (i < tokens.length) {\n    const token = tokens[i++];\n\n    if (token.type === \"text\") {\n      value += escapeText(token.value);\n      continue;\n    }\n\n    if (token.type === \"group\") {\n      value += `{${stringifyTokens(token.tokens)}}`;\n      continue;\n    }\n\n    if (token.type === \"param\") {\n      value += `:${name(token.name)}`;\n      continue;\n    }\n\n    if (token.type === \"wildcard\") {\n      value += `*${name(token.name)}`;\n      continue;\n    }\n\n    throw new TypeError(`Unknown token type: ${(token as any).type}`);\n  }\n\n  return value;\n}\n\n/**\n * Stringify token data into a path string.\n */\nexport function stringify(data: TokenData): string {\n  return stringifyTokens(data.tokens);\n}\n\n/**\n * Validate the parameter name contains valid ID characters.\n */\nfunction isNameSafe(name: string): boolean {\n  const [first, ...rest] = name;\n  return ID_START.test(first) && rest.every((char) => ID_CONTINUE.test(char));\n}\n\n/**\n * Validate the next token does not interfere with the current param name.\n */\nfunction isNextNameSafe(token: Token | undefined): boolean {\n  if (token && token.type === \"text\") return !ID_CONTINUE.test(token.value[0]);\n  return true;\n}\n"]}