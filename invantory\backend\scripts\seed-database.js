import mysql from 'mysql2';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config();

// MySQL connection
const db = mysql.createConnection({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASS || '',
  database: process.env.DB_NAME || 'inventoryDB',
});

// Connect to database
db.connect((err) => {
  if (err) {
    console.error('❌ MySQL connection error:', err);
    process.exit(1);
  } else {
    console.log('✅ MySQL Connected!');
    seedDatabase();
  }
});

async function seedDatabase() {
  try {
    // Read sample data
    const sampleDataPath = path.join(__dirname, '../data/sample-products.json');
    const sampleData = JSON.parse(fs.readFileSync(sampleDataPath, 'utf8'));
    
    console.log('🌱 Starting database seeding...');
    
    // Clear existing data
    await new Promise((resolve, reject) => {
      db.query('DELETE FROM products', (err) => {
        if (err) reject(err);
        else {
          console.log('🗑️  Cleared existing products');
          resolve();
        }
      });
    });
    
    // Reset auto increment
    await new Promise((resolve, reject) => {
      db.query('ALTER TABLE products AUTO_INCREMENT = 1', (err) => {
        if (err) reject(err);
        else {
          console.log('🔄 Reset auto increment');
          resolve();
        }
      });
    });
    
    // Insert sample products
    let insertedCount = 0;
    
    for (const product of sampleData.products) {
      await new Promise((resolve, reject) => {
        const query = `
          INSERT INTO products (name, sku, description, category, price, stock, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
        `;
        
        db.query(
          query,
          [product.name, product.sku, product.description, product.category, product.price, product.stock],
          (err, result) => {
            if (err) {
              console.error(`❌ Error inserting ${product.name}:`, err);
              reject(err);
            } else {
              insertedCount++;
              console.log(`✅ Inserted: ${product.name} (ID: ${result.insertId})`);
              resolve();
            }
          }
        );
      });
    }
    
    console.log(`\n🎉 Database seeding completed!`);
    console.log(`📊 Inserted ${insertedCount} products`);
    
    // Show summary
    db.query('SELECT COUNT(*) as total FROM products', (err, results) => {
      if (!err) {
        console.log(`📈 Total products in database: ${results[0].total}`);
      }
      
      db.query('SELECT category, COUNT(*) as count FROM products GROUP BY category', (err, results) => {
        if (!err) {
          console.log('\n📋 Products by category:');
          results.forEach(row => {
            console.log(`   ${row.category}: ${row.count} products`);
          });
        }
        
        console.log('\n🚀 You can now start the server with: npm start');
        db.end();
        process.exit(0);
      });
    });
    
  } catch (error) {
    console.error('❌ Seeding error:', error);
    db.end();
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n⚠️  Seeding interrupted');
  db.end();
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n⚠️  Seeding terminated');
  db.end();
  process.exit(1);
});
