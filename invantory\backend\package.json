{"name": "inventory-backend", "version": "1.0.0", "description": "REST API for Inventory Management System", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "setup-db": "mysql -u root -p < database/schema.sql", "seed": "node scripts/seed-database.js"}, "keywords": ["inventory", "api", "rest", "mysql", "express"], "author": "Your Name", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "mysql2": "^3.14.5"}, "devDependencies": {"nodemon": "^3.0.0"}}