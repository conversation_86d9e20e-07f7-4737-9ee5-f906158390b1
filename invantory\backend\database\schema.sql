-- Inventory Management Database Schema
-- Run this script to create the database and tables

CREATE DATABASE IF NOT EXISTS inventoryDB;
USE inventoryDB;

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    sku VARCHAR(100) UNIQUE,
    description TEXT,
    category VARCHAR(100),
    price DECIMAL(10, 2) DEFAULT 0.00,
    stock INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_category (category),
    INDEX idx_sku (sku),
    INDEX idx_stock (stock)
);

-- Sample data insertion
INSERT INTO products (name, sku, description, category, price, stock) VALUES
('Wireless Bluetooth Headphones', 'WBH-001', 'High-quality wireless headphones with noise cancellation', 'Electronics', 99.99, 25),
('Gaming Mechanical Keyboard', 'GMK-002', 'RGB backlit mechanical keyboard for gaming', 'Electronics', 149.99, 15),
('Ergonomic Office Chair', 'EOC-003', 'Comfortable office chair with lumbar support', 'Furniture', 299.99, 8),
('Stainless Steel Water Bottle', 'SSWB-004', '32oz insulated water bottle', 'Kitchen', 24.99, 50),
('Yoga Mat Premium', 'YMP-005', 'Non-slip yoga mat with carrying strap', 'Sports', 39.99, 30),
('Coffee Maker Deluxe', 'CMD-006', '12-cup programmable coffee maker', 'Kitchen', 89.99, 12),
('LED Desk Lamp', 'LDL-007', 'Adjustable LED desk lamp with USB charging port', 'Electronics', 45.99, 20),
('Running Shoes Men', 'RSM-008', 'Lightweight running shoes for men', 'Sports', 79.99, 18),
('Wireless Mouse', 'WM-009', 'Ergonomic wireless mouse with precision tracking', 'Electronics', 29.99, 35),
('Notebook Set', 'NS-010', 'Set of 3 lined notebooks for office use', 'Office', 12.99, 100);

-- Create indexes for better performance
CREATE INDEX idx_price ON products(price);
CREATE INDEX idx_created_at ON products(created_at);
CREATE INDEX idx_updated_at ON products(updated_at);

-- View for low stock products
CREATE VIEW low_stock_products AS
SELECT id, name, sku, category, stock, price
FROM products 
WHERE stock < 5
ORDER BY stock ASC;

-- View for product statistics by category
CREATE VIEW category_stats AS
SELECT 
    category,
    COUNT(*) as product_count,
    SUM(stock) as total_stock,
    AVG(price) as avg_price,
    SUM(price * stock) as total_value
FROM products 
WHERE category != ''
GROUP BY category
ORDER BY total_value DESC;
