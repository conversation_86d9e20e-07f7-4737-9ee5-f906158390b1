# Inventory Management API

A complete REST API for inventory management built with Node.js, Express, and MySQL.

## Features

- ✅ Complete CRUD operations for products
- ✅ Search and pagination
- ✅ Category management
- ✅ Stock alerts and low inventory tracking
- ✅ Bulk stock updates
- ✅ Inventory statistics
- ✅ Input validation and error handling
- ✅ MySQL database with optimized queries

## API Endpoints

### Products
- `GET /api/products` - Get all products with search, pagination, sorting
- `GET /api/products/:id` - Get single product
- `POST /api/products` - Create new product
- `PUT /api/products/:id` - Update product
- `DELETE /api/products/:id` - Delete product

### Categories
- `GET /api/categories` - Get all categories with product counts
- `GET /api/products/category/:category` - Get products by category

### Stock Management
- `GET /api/stock-alert` - Get low stock products
- `PATCH /api/products/bulk-stock` - Bulk update stock quantities

### Statistics
- `GET /api/stats` - Get inventory statistics
- `GET /api/health` - Health check

## Setup Instructions

### 1. Install Dependencies

```bash
cd invantory/backend
npm install
```

### 2. Install Development Dependencies

```bash
npm install --save-dev nodemon
```

### 3. Database Setup

Make sure MySQL is installed and running on your system.

1. Create the database and tables:
```bash
mysql -u root -p < database/schema.sql
```

Or manually run the SQL commands from `database/schema.sql` in your MySQL client.

2. Update your `.env` file with your MySQL credentials:
```env
DB_HOST=localhost
DB_USER=root
DB_PASS=yourpassword
DB_NAME=inventoryDB
PORT=5000
```

### 4. Start the Server

Development mode (with auto-restart):
```bash
npm run dev
```

Production mode:
```bash
npm start
```

The server will start on `http://localhost:5000`

## Testing the API

### Using curl

1. **Health Check**
```bash
curl "http://localhost:5000/api/health"
```

2. **Get All Products**
```bash
curl "http://localhost:5000/api/products"
```

3. **Search Products**
```bash
curl "http://localhost:5000/api/products?search=wireless&page=1&limit=5"
```

4. **Create Product**
```bash
curl -X POST "http://localhost:5000/api/products" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Product",
    "sku": "TEST-001",
    "description": "A test product",
    "category": "Electronics",
    "price": 29.99,
    "stock": 10
  }'
```

5. **Update Product**
```bash
curl -X PUT "http://localhost:5000/api/products/1" \
  -H "Content-Type: application/json" \
  -d '{
    "price": 39.99,
    "stock": 15
  }'
```

6. **Get Low Stock Products**
```bash
curl "http://localhost:5000/api/stock-alert?threshold=10"
```

7. **Get Statistics**
```bash
curl "http://localhost:5000/api/stats"
```

### Using Postman

Import the API documentation from `api-documentation.json` or manually test the endpoints using the examples above.

## Database Schema

The database includes:

- **products** table with fields:
  - `id` (Primary Key)
  - `name` (Required)
  - `sku` (Unique)
  - `description`
  - `category`
  - `price`
  - `stock`
  - `created_at`
  - `updated_at`

- **Indexes** for optimized queries on name, category, SKU, stock, and price
- **Views** for low stock products and category statistics

## Sample Data

The database schema includes sample data with 10 products across different categories. You can also find additional sample data in `data/sample-products.json`.

## Error Handling

The API includes comprehensive error handling:

- **400** - Bad Request (invalid input)
- **404** - Not Found (resource doesn't exist)
- **409** - Conflict (duplicate SKU)
- **500** - Internal Server Error

All errors are logged to the console with detailed information.

## Development

### Project Structure
```
backend/
├── server.js              # Main server file
├── package.json           # Dependencies and scripts
├── .env                   # Environment variables
├── README.md             # This file
├── database/
│   └── schema.sql        # Database schema and sample data
├── data/
│   └── sample-products.json  # Additional sample data
└── api-documentation.json    # Complete API documentation
```

### Adding New Features

1. Add new routes in `server.js`
2. Update the API documentation in `api-documentation.json`
3. Add any new database changes to `database/schema.sql`
4. Test thoroughly with curl or Postman

## Production Deployment

1. Set up MySQL database on your production server
2. Update `.env` with production database credentials
3. Run `npm start` to start the server
4. Consider using PM2 for process management:
   ```bash
   npm install -g pm2
   pm2 start server.js --name "inventory-api"
   ```

## License

MIT License
