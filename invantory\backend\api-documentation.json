{"apiDocumentation": {"title": "Inventory Management API", "version": "1.0.0", "description": "Complete REST API for inventory management system with MySQL database", "baseUrl": "http://localhost:5000/api", "endpoints": [{"method": "GET", "path": "/health", "description": "Health check endpoint", "response": {"status": "OK", "message": "Inventory API is running", "timestamp": "2024-01-01T00:00:00.000Z"}}, {"method": "GET", "path": "/products", "description": "Get all products with search, pagination, and sorting", "queryParameters": {"search": "Search term for name or description", "page": "Page number (default: 1)", "limit": "Items per page (default: 10)", "sortBy": "Sort field: id, name, price, stock, created_at (default: id)", "sortOrder": "Sort order: ASC or DESC (default: ASC)"}, "example": "/products?search=wireless&page=1&limit=5&sortBy=price&sortOrder=DESC", "response": {"products": [], "pagination": {"page": 1, "limit": 10, "total": 50, "totalPages": 5}}}, {"method": "GET", "path": "/products/:id", "description": "Get single product by ID", "example": "/products/1", "response": {"id": 1, "name": "Product Name", "sku": "SKU-001", "description": "Product description", "category": "Electronics", "price": 99.99, "stock": 25, "created_at": "2024-01-01T00:00:00.000Z", "updated_at": "2024-01-01T00:00:00.000Z"}}, {"method": "POST", "path": "/products", "description": "Create new product", "requestBody": {"name": "Product Name (required)", "sku": "Unique SKU (optional)", "description": "Product description (optional)", "category": "Product category (optional)", "price": "Product price (optional, default: 0)", "stock": "Stock quantity (optional, default: 0)"}, "example": {"name": "New Product", "sku": "NP-001", "description": "A new product", "category": "Electronics", "price": 49.99, "stock": 10}, "response": {"id": 16, "name": "New Product", "sku": "NP-001", "description": "A new product", "category": "Electronics", "price": 49.99, "stock": 10, "created_at": "2024-01-01T00:00:00.000Z", "updated_at": "2024-01-01T00:00:00.000Z"}}, {"method": "PUT", "path": "/products/:id", "description": "Update existing product", "requestBody": {"name": "Updated name (optional)", "sku": "Updated SKU (optional)", "description": "Updated description (optional)", "category": "Updated category (optional)", "price": "Updated price (optional)", "stock": "Updated stock (optional)"}, "example": {"name": "Updated Product Name", "price": 59.99, "stock": 15}}, {"method": "DELETE", "path": "/products/:id", "description": "Delete product by ID", "example": "/products/1", "response": {"message": "Product deleted successfully", "deletedProduct": {}}}, {"method": "GET", "path": "/stock-alert", "description": "Get products with low stock", "queryParameters": {"threshold": "Stock threshold (default: 5)"}, "example": "/stock-alert?threshold=10", "response": {"threshold": 10, "lowStockProducts": [], "count": 3}}, {"method": "GET", "path": "/products/category/:category", "description": "Get products by category with pagination", "queryParameters": {"page": "Page number (default: 1)", "limit": "Items per page (default: 10)"}, "example": "/products/category/Electronics?page=1&limit=5", "response": {"category": "Electronics", "products": [], "pagination": {"page": 1, "limit": 5, "total": 15, "totalPages": 3}}}, {"method": "GET", "path": "/categories", "description": "Get all categories with product counts", "response": {"categories": [{"category": "Electronics", "productCount": 5}], "totalCategories": 4}}, {"method": "PATCH", "path": "/products/bulk-stock", "description": "Bulk update stock quantities", "requestBody": {"updates": [{"id": 1, "stock": 50}, {"id": 2, "stock": 25}]}, "response": {"message": "Bulk stock update completed", "updates": [], "totalUpdated": 2}}, {"method": "GET", "path": "/stats", "description": "Get inventory statistics", "response": {"statistics": {"totalProducts": 15, "totalValue": 5000.5, "lowStock": 3, "categories": 5, "avgPrice": 75.25, "totalStock": 500}, "generatedAt": "2024-01-01T00:00:00.000Z"}}], "errorResponses": {"400": {"description": "Bad Request - Invalid input data", "example": {"error": "Name is required and must be a non-empty string"}}, "404": {"description": "Not Found - Resource not found", "example": {"error": "Product not found"}}, "409": {"description": "Conflict - Duplicate resource", "example": {"error": "Product with this SKU already exists"}}, "500": {"description": "Internal Server Error", "example": {"error": "Failed to fetch products"}}}, "testingExamples": {"curl": {"getAllProducts": "curl \"http://localhost:5000/api/products\"", "searchProducts": "curl \"http://localhost:5000/api/products?search=wireless&page=1&limit=5\"", "getProduct": "curl \"http://localhost:5000/api/products/1\"", "createProduct": "curl -X POST \"http://localhost:5000/api/products\" -H \"Content-Type: application/json\" -d '{\"name\":\"Test Product\",\"price\":29.99,\"stock\":10}'", "updateProduct": "curl -X PUT \"http://localhost:5000/api/products/1\" -H \"Content-Type: application/json\" -d '{\"price\":39.99,\"stock\":15}'", "deleteProduct": "curl -X DELETE \"http://localhost:5000/api/products/1\"", "lowStock": "curl \"http://localhost:5000/api/stock-alert?threshold=10\"", "bulkUpdate": "curl -X PATCH \"http://localhost:5000/api/products/bulk-stock\" -H \"Content-Type: application/json\" -d '{\"updates\":[{\"id\":1,\"stock\":50},{\"id\":2,\"stock\":25}]}'", "stats": "curl \"http://localhost:5000/api/stats\""}}}}