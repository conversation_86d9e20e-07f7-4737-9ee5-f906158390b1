import { Link } from "react-router-dom";
import inventory from "../assets/inventoryNav.ico";

export default function NavBar({ children }) {
  return (
    <>
      <nav className="navbar navbar-expand-sm bg-dark navbar-dark">
        <div className="container-fluid">
          {/* Left side brand */}
          <Link
            to="/"
            className="navbar-brand ms-3 fw-bold d-flex align-items-center"
          >
            <img
              src={inventory}
              alt="Logo"
              style={{ width: "40px" }}
              className="me-2"
            />
            Inventory
          </Link>

          {/* Right side navigation */}
          <ul className="navbar-nav ms-auto">
            <li className="nav-item">
              <Link className="nav-link fw-bold" to="/">
                Products
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link fw-bold" to="/add-product">
                Add Product
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link fw-bold" to="/stock-alert">
                Stock Alert
              </Link>
            </li>
          </ul>
        </div>
      </nav>

      <div>{children}</div>
    </>
  );
}
