import "./App.css";
import Product from "./Components/Product";
import NavBar from "./Components/NavBar";
import { BrowserRouter, Routes, Route } from "react-router-dom";

function App() {
  return (
    <BrowserRouter>
      <NavBar>
        <Routes>
          <Route path="/" element={<Product />} />

          {/* <Route path="/add-product" element={<AddProduct />} /> */}
          {/* <Route path="/stock-alert" element={<StockAlert />} /> */}
        </Routes>
      </NavBar>
    </BrowserRouter>
  );
}

export default App;
