/* Common reset */
.btn-edit,
.btn-delete {
    border-radius: 6px;
    padding: 6px 10px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    /* smooth hover effect */
}

/* --- Edit Button --- */
.btn-edit {
    background-color: #90c9ff !important;
    /* Light blue */
    border-color: #90c9ff !important;
}

.btn-edit img {
    filter: brightness(0) invert(1);
    /* white icon */
}

.btn-edit:hover {
    background-color: #007bff !important;
    /* Darker blue */
    border-color: #007bff !important;
}

/* --- Delete Button --- */
.btn-delete {
    background-color: #f5a6a6 !important;
    /* Light red/pink */
    border-color: #f5a6a6 !important;
}

.btn-delete img {
    filter: brightness(0) invert(1);
    /* white icon */
}

.btn.btn-delete:hover {
    background-color: #dc3545 !important;
    /* Darker red */
    border-color: #dc3545 !important;
}